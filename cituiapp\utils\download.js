/**
 * 跨平台下载链接处理工具
 * 支持H5、APP、微信小程序三个平台的差异化处理
 */

/**
 * 验证URL是否为有效的下载链接
 * @param {string} url - 下载链接
 * @returns {boolean} 是否为有效的下载链接
 */
export const isValidDownloadUrl = (url) => {
    if (!url || typeof url !== 'string') {
        return false
    }
    
    // 检查是否以http或https开头
    return url.startsWith('http://') || url.startsWith('https://')
}

/**
 * 跨平台下载处理方法
 * @param {string} downloadUrl - 下载链接
 * @param {string} appName - 应用名称（可选，用于提示信息）
 */
export const handleDownload = (downloadUrl, appName = '应用') => {
    // 验证下载链接
    if (!isValidDownloadUrl(downloadUrl)) {
        uni.showToast({
            title: '下载链接无效',
            icon: 'none',
            duration: 2000
        })
        return
    }

    // #ifdef APP-PLUS
    // APP平台：使用plus.runtime.openURL()调用手机默认浏览器
    try {
        plus.runtime.openURL(downloadUrl)
        uni.showToast({
            title: `正在打开${appName}下载页面`,
            icon: 'success',
            duration: 2000
        })
    } catch (error) {
        console.error('APP平台打开下载链接失败:', error)
        uni.showToast({
            title: '打开下载页面失败',
            icon: 'none',
            duration: 2000
        })
    }
    // #endif

    // #ifdef H5
    // H5平台：使用window.open()直接跳转
    try {
        window.open(downloadUrl, '_blank')
        uni.showToast({
            title: `正在跳转到${appName}下载页面`,
            icon: 'success',
            duration: 2000
        })
    } catch (error) {
        console.error('H5平台打开下载链接失败:', error)
        uni.showToast({
            title: '打开下载页面失败',
            icon: 'none',
            duration: 2000
        })
    }
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序：复制链接到剪贴板并提示用户
    try {
        uni.setClipboardData({
            data: downloadUrl,
            success: () => {
                uni.showModal({
                    title: '下载提示',
                    content: `${appName}的下载链接已复制到剪贴板，请在浏览器中打开下载`,
                    showCancel: false,
                    confirmText: '我知道了',
                    confirmColor: '#007aff'
                })
            },
            fail: (error) => {
                console.error('微信小程序复制链接失败:', error)
                uni.showToast({
                    title: '复制链接失败',
                    icon: 'none',
                    duration: 2000
                })
            }
        })
    } catch (error) {
        console.error('微信小程序处理下载链接失败:', error)
        uni.showToast({
            title: '处理下载链接失败',
            icon: 'none',
            duration: 2000
        })
    }
    // #endif

    // #ifdef MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
    // 其他小程序平台：复制链接到剪贴板并提示用户
    try {
        uni.setClipboardData({
            data: downloadUrl,
            success: () => {
                uni.showModal({
                    title: '下载提示',
                    content: `${appName}的下载链接已复制到剪贴板，请在浏览器中打开下载`,
                    showCancel: false,
                    confirmText: '我知道了'
                })
            },
            fail: (error) => {
                console.error('小程序复制链接失败:', error)
                uni.showToast({
                    title: '复制链接失败',
                    icon: 'none',
                    duration: 2000
                })
            }
        })
    } catch (error) {
        console.error('小程序处理下载链接失败:', error)
        uni.showToast({
            title: '处理下载链接失败',
            icon: 'none',
            duration: 2000
        })
    }
    // #endif
}

/**
 * 获取当前平台信息（用于调试）
 * @returns {string} 当前平台名称
 */
export const getCurrentPlatform = () => {
    // #ifdef APP-PLUS
    return 'APP'
    // #endif
    
    // #ifdef H5
    return 'H5'
    // #endif
    
    // #ifdef MP-WEIXIN
    return '微信小程序'
    // #endif
    
    // #ifdef MP-ALIPAY
    return '支付宝小程序'
    // #endif
    
    // #ifdef MP-BAIDU
    return '百度小程序'
    // #endif
    
    // #ifdef MP-TOUTIAO
    return '字节跳动小程序'
    // #endif
    
    // #ifdef MP-QQ
    return 'QQ小程序'
    // #endif
    
    return '未知平台'
}
