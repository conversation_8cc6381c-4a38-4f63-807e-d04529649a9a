# UNIAPP 安全区域适配方案

## 概述

本方案为UNIAPP项目提供了统一的安全区域适配解决方案，专门用于处理自定义导航栏页面的顶部安全距离问题。该方案兼容APP、小程序、H5三个平台，能够自动适配不同设备的状态栏高度和安全区域。

## 方案架构

### 1. 核心工具类 (`cituiapp/common/utils/tools.js`)

提供了以下核心功能：
- `getSystemSafeArea()`: 获取系统信息和安全区域信息
- `pxToRpx()`: 将px转换为rpx
- `getTopSafeDistanceRpx()`: 获取顶部安全距离的rpx值

### 2. 安全区域混入 (`cituiapp/mixins/safeArea.js`)

提供了统一的安全区域处理逻辑：
- 自动初始化安全区域信息
- 提供计算属性用于样式绑定
- 支持页面级别的安全区域控制

### 3. 配置文件 (`cituiapp/config/safeArea.js`)

配置需要安全区域适配的页面列表：
- 集中管理需要适配的页面
- 提供页面检查工具函数

## 已适配页面

以下页面已完成安全区域适配：

1. **首页** (`pages/index/index.vue`)
2. **线索页** (`pages/clue/clue.vue`)
3. **详情页** (`pages/detail/detail.vue`)
4. **评测页** (`pages/evaluation/evaluation.vue`)
5. **提交线索页** (`pages/submit-clue/submit-clue.vue`)
6. **提交报告页** (`pages/submit-report/submit-report.vue`)

## 使用方法

### 为新页面添加安全区域适配

1. **引入混入**：
```javascript
import safeAreaMixin from '@/mixins/safeArea.js'

export default {
  mixins: [safeAreaMixin],
  // 其他配置...
}
```

2. **在模板中使用**：
```vue
<template>
  <view class="page-container">
    <!-- 固定头部 -->
    <view class="fixed-header" :style="topSafeAreaStyle">
      <!-- 头部内容 -->
    </view>
    
    <!-- 内容区域 -->
    <view class="content-container" :style="contentTopMarginStyle">
      <!-- 页面内容 -->
    </view>
  </view>
</template>
```

3. **更新配置文件**：
在 `cituiapp/config/safeArea.js` 中添加新页面路径：
```javascript
export const SAFE_AREA_PAGES = [
  // 现有页面...
  'pages/your-new-page/your-new-page'
]
```

4. **调整CSS样式**：
移除固定的 `padding-top` 值，改为动态计算：
```scss
.content-container {
  /* 移除固定值：padding-top: 120rpx; */
  /* padding-top 通过动态样式设置，适配不同设备的安全区域 */
  height: 100vh;
}
```

## 平台兼容性

### APP环境
- 自动获取状态栏高度
- 添加标准导航栏高度(44px)
- 完美适配刘海屏和异形屏

### 微信小程序
- 获取胶囊按钮位置信息
- 基于胶囊按钮底部计算安全距离
- 兼容不同版本的微信客户端

### H5环境
- 保留状态栏高度处理
- 兼容移动端浏览器
- 支持PWA应用

## 技术特点

1. **统一管理**: 所有安全区域逻辑集中在混入中，便于维护
2. **自动适配**: 无需手动计算，自动适配不同设备
3. **配置灵活**: 通过配置文件控制哪些页面需要适配
4. **性能优化**: 使用计算属性，避免重复计算
5. **向后兼容**: 不影响现有页面的正常显示

## 注意事项

1. 只有在 `pages.json` 中配置了 `"navigationStyle": "custom"` 的页面才需要安全区域适配
2. 混入会在页面创建时自动初始化安全区域信息
3. 如果页面有特殊需求，可以重写混入中的方法
4. 建议在开发时在不同设备上测试适配效果

## 故障排除

### 常见问题

1. **安全距离不正确**
   - 检查是否正确引入混入
   - 确认页面路径是否在配置文件中
   - 查看控制台是否有错误信息

2. **样式不生效**
   - 确认模板中使用了正确的样式绑定
   - 检查CSS中是否移除了固定的padding-top值

3. **平台兼容问题**
   - 在对应平台上测试
   - 查看工具类中的平台判断逻辑

## 维护说明

- 新增需要适配的页面时，记得更新配置文件
- 如需调整安全距离计算逻辑，修改工具类中的相关函数
- 定期在不同设备和平台上测试适配效果
