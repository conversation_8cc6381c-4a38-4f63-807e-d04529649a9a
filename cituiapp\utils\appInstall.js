/**
 * APP安装状态检测和启动工具类
 * 仅在UNIAPP APP环境（Android/iOS）中执行检测逻辑
 */

import { handleDownload } from '@/utils/download.js'

/**
 * 检查当前是否为APP环境
 * @returns {boolean} 是否为APP环境
 */
export const isAppEnvironment = () => {
    // #ifdef APP-PLUS
    const systemInfo = uni.getSystemInfoSync()
    return systemInfo.platform === 'android' || systemInfo.platform === 'ios'
    // #endif
    
    // #ifndef APP-PLUS
    return false
    // #endif
}

/**
 * 检查APP是否已安装
 * @param {string} packageName - APP包名（Android）或URL Scheme（iOS）
 * @returns {Promise<boolean>} APP是否已安装
 */
export const checkAppInstalled = (packageName) => {
    return new Promise((resolve) => {
        // 非APP环境直接返回false
        if (!isAppEnvironment()) {
            resolve(false)
            return
        }
        
        // 检查包名是否有效
        if (!packageName || typeof packageName !== 'string') {
            resolve(false)
            return
        }
        
        // #ifdef APP-PLUS
        // 仅App端可用
        if (typeof plus !== 'undefined') {
            plus.runtime.isApplicationExist({
                pname: packageName, // Android包名或iOS的URL Scheme
                action: '' // iOS可选
            }, (exist) => {
                resolve(exist)
            })
        } else {
            resolve(false)
        }
        // #endif
        
        // #ifndef APP-PLUS
        resolve(false)
        // #endif
    })
}

/**
 * 通过包名启动APP
 * @param {string} packageName - APP包名（Android）或URL Scheme（iOS）
 * @param {string} appName - APP名称（用于错误提示）
 * @returns {Promise<boolean>} 是否成功启动
 */
export const openAppByPackageName = (packageName, appName = 'APP') => {
    return new Promise((resolve) => {
        // 非APP环境直接返回false
        if (!isAppEnvironment()) {
            resolve(false)
            return
        }
        
        // 检查包名是否有效
        if (!packageName || typeof packageName !== 'string') {
            uni.showToast({
                title: '包名无效',
                icon: 'none'
            })
            resolve(false)
            return
        }
        
        // #ifdef APP-PLUS
        if (typeof plus !== 'undefined') {
            plus.runtime.launchApplication({
                pname: packageName, // 目标应用的包名
            }, function(e) {
                console.log('打开应用失败:', e.message)
                uni.showToast({
                    title: `打开${appName}失败`,
                    icon: 'none'
                })
                resolve(false)
            })
            
            // 成功启动（没有回调表示成功）
            setTimeout(() => {
                resolve(true)
            }, 500)
        } else {
            resolve(false)
        }
        // #endif
        
        // #ifndef APP-PLUS
        resolve(false)
        // #endif
    })
}

/**
 * 批量检测APP列表的安装状态
 * @param {Array} appList - APP列表，每个项目需要包含package字段
 * @returns {Promise<Array>} 更新了isInstalled字段的APP列表
 */
export const batchCheckAppsInstalled = async (appList) => {
    if (!Array.isArray(appList) || appList.length === 0) {
        return appList
    }
    
    // 非APP环境直接返回原列表，所有APP标记为未安装
    if (!isAppEnvironment()) {
        return appList.map(app => ({
            ...app,
            isInstalled: false
        }))
    }
    
    // 并发检测所有APP的安装状态
    const checkPromises = appList.map(async (app) => {
        if (app.package) {
            const isInstalled = await checkAppInstalled(app.package)
            return {
                ...app,
                isInstalled
            }
        } else {
            return {
                ...app,
                isInstalled: false
            }
        }
    })
    
    try {
        const updatedAppList = await Promise.all(checkPromises)
        return updatedAppList
    } catch (error) {
        console.error('批量检测APP安装状态失败:', error)
        // 出错时返回原列表，所有APP标记为未安装
        return appList.map(app => ({
            ...app,
            isInstalled: false
        }))
    }
}

/**
 * 统一的APP操作处理方法
 * @param {Object} appInfo - APP信息对象
 * @param {string} appInfo.package - APP包名
 * @param {string} appInfo.download_url - 下载链接
 * @param {string} appInfo.name - APP名称
 * @param {boolean} appInfo.isInstalled - 是否已安装
 * @param {string} defaultButtonText - 默认按钮文字（如"下载赚钱"或"立即赚钱"）
 */
export const handleAppAction = async (appInfo, defaultButtonText = '下载赚钱') => {
    if (!appInfo) {
        uni.showToast({
            title: 'APP信息无效',
            icon: 'none'
        })
        return
    }
    
    // 非APP环境或APP未安装，执行下载逻辑
    if (!isAppEnvironment() || !appInfo.isInstalled) {
        handleDownload(appInfo.download_url, appInfo.name)
        return
    }
    
    // APP已安装，尝试打开APP
    if (appInfo.package) {
        const success = await openAppByPackageName(appInfo.package, appInfo.name)
        if (success) {
            uni.showToast({
                title: `正在打开${appInfo.name}`,
                icon: 'success'
            })
        }
    } else {
        // 没有包名信息，降级到下载逻辑
        handleDownload(appInfo.download_url, appInfo.name)
    }
}

/**
 * 获取按钮显示文字
 * @param {boolean} isInstalled - 是否已安装
 * @param {string} defaultText - 默认文字（如"下载赚钱"或"立即赚钱"）
 * @returns {string} 按钮显示文字
 */
export const getButtonText = (isInstalled, defaultText = '下载赚钱') => {
    // 非APP环境显示默认文字
    if (!isAppEnvironment()) {
        return defaultText
    }
    
    // APP环境根据安装状态显示不同文字
    return isInstalled ? '打开赚钱' : defaultText
}

/**
 * 检测单个APP的安装状态并更新
 * @param {Object} appInfo - APP信息对象
 * @returns {Promise<Object>} 更新了isInstalled字段的APP信息
 */
export const checkSingleAppInstalled = async (appInfo) => {
    if (!appInfo || !appInfo.package) {
        return {
            ...appInfo,
            isInstalled: false
        }
    }
    
    const isInstalled = await checkAppInstalled(appInfo.package)
    return {
        ...appInfo,
        isInstalled
    }
}
