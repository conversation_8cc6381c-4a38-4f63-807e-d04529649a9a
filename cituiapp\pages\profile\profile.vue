<template>
	<view class="page-container">
		<!-- 内容区域 - 固定高度布局 -->
		<view class="content-wrapper">
				<!-- 用户信息头部区域 -->
				<view class="user-header-section">
					<!-- 用户基本信息 -->
					<view class="user-info-card">
						<view class="user-avatar-container">
							<image 
								:src="userInfo.avatar_url" 
								class="user-avatar"
								mode="aspectFill"
							></image>
							<view class="avatar-border"></view>
						</view>
						<view class="user-details">
							<view class="user-name">{{ userInfo.phone }}</view>
							<!-- <view class="user-id-row">
								<text class="id-label">ID: {{ userInfo.id }}</text>
								<view class="copy-btn" @click="copyUserId">
									<text class="copy-icon">📋</text>
								</view>
							</view> -->
						</view>
					</view>

				</view>

							<!-- 功能入口区域 -->
			<view class="function-section">
				<view class="function-grid">
						<view
							v-for="(item, index) in dynamicFunctionList"
							:key="index"
							class="function-item"
							@click="handleFunctionClick(item)"
						>
							<view class="function-icon" :class="item.iconClass">
								<text class="icon-symbol">{{ item.icon }}</text>
							</view>
							<view class="function-info">
								<text class="function-name">{{ item.name }}</text>
								<text class="function-desc">{{ item.desc }}</text>
							</view>
						</view>
					</view>
				</view>



							<!-- 底部安全区域 -->
			<view class="safe-area-bottom"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userStats: {
					report_count: 0,
					clue_count: 0
				},
				functionList: [
					{
						name: '我的报告',
						desc: '已提交0份',
						icon: '📄',
						iconClass: 'icon-report',
						action: 'reports'
					},
					{
						name: '我的线索',
						desc: '已提交0条',
						icon: '💧',
						iconClass: 'icon-clue',
						action: 'clues'
					},
					{
						name: '退出登录',
						desc: '安全退出账号',
						icon: '🚪',
						iconClass: 'icon-logout',
						action: 'logout'
					}
				]
			}
		},
		computed: {
			userInfo() {
				const vuexUserInfo = this.$store.getters.getUserInfo
				const defaultAvatar = '/static/img/citui.png'

				// 如果Vuex中有用户信息
				if (vuexUserInfo && Object.keys(vuexUserInfo).length > 0) {
					// 检查avatar_url是否存在且有效
					const avatarUrl = vuexUserInfo.avatar_url
					const hasValidAvatar = avatarUrl && avatarUrl !== null && avatarUrl !== undefined && avatarUrl !== ''

					return {
						...vuexUserInfo,
						avatar_url: hasValidAvatar ? avatarUrl : defaultAvatar
					}
				}

				// 用户未登录或信息不存在，使用默认值
				return {
					phone: '游客',
					avatar_url: defaultAvatar
				}
			},

			// 动态更新功能列表的描述
			dynamicFunctionList() {
				return this.functionList.map(item => {
					if (item.action === 'reports') {
						return {
							...item,
							desc: `已提交${this.userStats.report_count}份`
						}
					} else if (item.action === 'clues') {
						return {
							...item,
							desc: `已提交${this.userStats.clue_count}条`
						}
					}
					return item
				})
			}
		},
		methods: {
			// 加载用户数据
			loadUserData() {
				// 这里可以调用API获取用户数据
				console.log('加载用户数据')
				this.loadUserStats()
			},

			// 加载用户统计数据
			loadUserStats() {
				uni.$u.http.get('/user/stats',{
					custom: {
						auth: true
					}
				}).then(res => {
					console.log('用户统计数据:', res)
					this.userStats = res
				}).catch(err => {
					console.error('获取用户统计数据失败:', err)
					uni.showToast({
						title: '获取统计数据失败',
						icon: 'none'
					})
				})
			},
			
			// 检查登录状态
			checkLoginStatus() {
				const isLoggedIn = this.$store.getters.getLoginStatus
				if (!isLoggedIn) {
					// 用户未登录，跳转到登录页面
					uni.reLaunch({
						url: '/pages/login/login',
						complete: () => {
							console.log('已跳转到登录页面')
						}
					})
					return false
				}
				return true
			},
			
			// 复制用户ID
			copyUserId() {
				uni.setClipboardData({
					data: this.userInfo.id,
					success: () => {
						uni.showToast({
							title: '用户ID已复制',
							icon: 'success'
						})
					}
				})
			},
			

			
			// 退出登录
			handleLogout() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出当前账号吗？',
					success: (res) => {
						if (res.confirm) {
							// 使用Vuex清除用户状态
							this.$store.dispatch('logout')
							
							// 跳转到登录页面
							uni.reLaunch({
								url: '/pages/login/login'
							})
						}
					}
				})
			},
			
			// 功能点击
			handleFunctionClick(item) {
				console.log('功能点击:', item.name)
				
				switch(item.action) {
					case 'reports':
						break
					case 'clues':
						break
					case 'logout':
						this.handleLogout()
						break
					default:
				}
			}
		},
		
		onLoad() {
			// 页面加载时检查登录状态
			if (!this.checkLoginStatus()) {
				return // 如果未登录，直接返回，不再执行后续操作
			}
			
			// 已登录，获取用户数据
			this.loadUserData()
		},
		
		onShow() {
			// 页面显示时再次检查登录状态（处理从其他页面返回的情况）
			this.checkLoginStatus()
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
}

/* 内容区域 */
.content-wrapper {
	height: 100vh;
	color: #ffffff;
	display: flex;
	flex-direction: column;
}

/* 用户信息头部区域 */
.user-header-section {
	padding: 60rpx 40rpx 32rpx;
	
	/* 用户基本信息卡片 */
	.user-info-card {
		display: flex;
		align-items: center;
		
		.user-avatar-container {
			position: relative;
			margin-right: 32rpx;
			width: 160rpx;
			height: 160rpx;
			
			.user-avatar {
				width: 100%;
				height: 100%;
				border-radius: 50%;
				background-color: #e5e7eb;
				display: block;
			}
			
			.avatar-border {
				position: absolute;
				top: -8rpx;
				left: -8rpx;
				width: calc(100% + 16rpx);
				height: calc(100% + 16rpx);
				border: 4rpx solid #fbbf24;
				border-radius: 50%;
				pointer-events: none;
				box-sizing: border-box;
			}
		}
		
		.user-details {
			flex: 1;
			
			.user-name {
				font-size: 40rpx;
				font-weight: bold;
				color: #ffffff;
				margin-bottom: 8rpx;
			}
			
			.user-id-row {
				display: flex;
				align-items: center;
				
				.id-label {
					font-size: 28rpx;
					color: #ffffff;
					margin-right: 16rpx;
				}
				
				.copy-btn {
					padding: 8rpx;
					
					.copy-icon {
						font-size: 24rpx;
						color: #ffffff;
					}
				}
			}
		}
	}
}

/* 功能入口区域 */
.function-section {
	margin: 32rpx 40rpx;
	
	.function-grid {
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		
		.function-item {
			background: rgba(255, 255, 255, 0.1);
			border-radius: 24rpx;
			padding: 40rpx 32rpx;
			display: flex;
			align-items: center;
			backdrop-filter: blur(10rpx);
			
			.function-icon {
				width: 80rpx;
				height: 80rpx;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 32rpx;
				
				&.icon-report {
					background: rgba(251, 191, 36, 0.2);
				}
				
				&.icon-clue {
					background: rgba(59, 130, 246, 0.2);
				}
				
				&.icon-logout {
					background: rgba(239, 68, 68, 0.2);
				}
				
				.icon-symbol {
					font-size: 40rpx;
				}
			}
			
			.function-info {
				flex: 1;
				
				.function-name {
					display: block;
					font-size: 36rpx;
					font-weight: 600;
					color: #ffffff;
					margin-bottom: 8rpx;
				}
				
				.function-desc {
					display: block;
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.7);
				}
			}
		}
	}
}

/* 底部安全区域 */
.safe-area-bottom {
	height: 120rpx; /* 为底部导航留出空间 */
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style>
