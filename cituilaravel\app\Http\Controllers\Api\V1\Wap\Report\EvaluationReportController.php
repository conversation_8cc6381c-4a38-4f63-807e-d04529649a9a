<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\Report;

use App\Http\Controllers\Api\Controller;
use App\Service\Report\EvaluationReportService;
use Illuminate\Http\JsonResponse;

class EvaluationReportController extends Controller
{
    protected EvaluationReportService $evaluationReportService;

    public function __construct(EvaluationReportService $evaluationReportService)
    {
        $this->evaluationReportService = $evaluationReportService;
        parent::__construct();
    }

    /**
     * 提交评测报告
     *
     * @return JsonResponse
     */
    public function submit(): JsonResponse
    {

        $result = $this->evaluationReportService->submitReport();
        return $this->apiSuccess($result, 200, '评测报告提交成功');

    }

    /**
     * 获取评测报告列表
     *
     * @return JsonResponse
     */
    public function getEvaluationList(): JsonResponse
    {
        $result = $this->evaluationReportService->getEvaluationList();
        return $this->apiSuccess($result);
    }

    /**
     * 获取评测报告详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getDetail(int $id): JsonResponse
    {
        $result = $this->evaluationReportService->getReportDetail($id);
        return $this->apiSuccess($result);
    }

    /**
     * 更新评测报告
     *
     * @param int $id
     * @return JsonResponse
     */
    public function update(int $id): JsonResponse
    {
        $result = $this->evaluationReportService->updateReport($id);
        return $this->apiSuccess($result, 200, '评测报告更新成功');
    }

    /**
     * 删除评测报告
     *
     * @param int $id
     * @return JsonResponse
     */
    public function delete(int $id): JsonResponse
    {
        $result = $this->evaluationReportService->deleteReport($id);
        return $this->apiSuccess($result, 200, '评测报告删除成功');
    }
}
