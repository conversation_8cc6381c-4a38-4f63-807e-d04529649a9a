/**
 * 安全区域混入
 * 用于自动处理自定义导航栏页面的顶部安全距离适配
 */

import { getSystemSafeArea, getTopSafeDistanceRpx } from '@/common/utils/tools.js'

export default {
  data() {
    return {
      // 安全区域相关数据
      safeAreaInfo: {
        statusBarHeight: 0,
        topSafeDistance: 0,
        topSafeDistanceRpx: 0,
        platform: 'unknown'
      },
      // 安全区域样式保护定时器
      safeAreaProtectionTimer: null
    }
  },
  
  computed: {
    // 计算顶部安全距离样式
    topSafeAreaStyle() {
      return {
        paddingTop: `${this.safeAreaInfo.topSafeDistanceRpx}rpx`
      }
    },

    // 计算固定头部的高度样式
    fixedHeaderStyle() {
      return {
        height: `${this.safeAreaInfo.topSafeDistanceRpx}rpx`,
        paddingTop: `${this.safeAreaInfo.statusBarHeight * 2}rpx` // 状态栏高度转rpx
      }
    },

    // 计算内容区域的顶部边距 - 基础版本（只考虑安全区域）
    contentTopMarginStyle() {
      return {
        marginTop: `${this.safeAreaInfo.topSafeDistanceRpx}rpx`
      }
    },

    // 计算内容区域的顶部边距 - 简单固定头部版本（如 index.vue, detail.vue）
    // 固定头部高度：安全区域 + 120rpx（header-content的min-height）
    contentTopMarginStyleWithSimpleHeader() {
      const totalHeight = this.safeAreaInfo.topSafeDistanceRpx + 120
      return {
        marginTop: `${totalHeight}rpx`
      }
    },

    // 计算内容区域的顶部边距 - 中等固定头部版本（如 submit-clue.vue, submit-report.vue）
    // 固定头部高度：安全区域 + 88rpx（navbar的height）
    contentTopMarginStyleWithMediumHeader() {
      const totalHeight = this.safeAreaInfo.topSafeDistanceRpx + 88
      return {
        marginTop: `${totalHeight}rpx`
      }
    },

    // 计算内容区域的顶部边距 - 复杂固定头部版本（如 clue.vue）
    // 固定头部高度计算：
    // 1. 固定头部padding: 24rpx(上) + 16rpx(下) = 40rpx
    // 2. 搜索框区域: 80rpx(min-height) + 32rpx(padding上下) + 16rpx(margin-bottom) = 128rpx
    // 3. 标签导航区域: 32rpx(文字) + 16rpx(padding-bottom) + 16rpx(section padding-bottom) = 64rpx
    // 总计：安全区域 + 232rpx - 20rpx(微调减少空白) = 212rpx
    contentTopMarginStyleWithComplexHeader() {
      const totalHeight = this.safeAreaInfo.topSafeDistanceRpx + 212
      return {
        marginTop: `${totalHeight}rpx`
      }
    },

    // 计算内容区域的顶部边距 - 复杂固定头部版本增强（如 evaluation.vue）
    // 与复杂头部版本相同，但增加额外间距避免重叠
    // 总计：安全区域 + 232rpx + 20rpx(微调增加间距) = 252rpx
    contentTopMarginStyleWithComplexHeaderEnhanced() {
      const totalHeight = this.safeAreaInfo.topSafeDistanceRpx + 272
      return {
        marginTop: `${totalHeight}rpx`
      }
    },

    // 【新增】计算内容区域的准确高度 - 复杂固定头部版本增强（推荐用于 evaluation.vue）
    // 使用 calc() 动态计算高度，避免 marginTop + height: 100vh 的布局冲突
    // 优势：高度计算更精确，避免内容超出视口，响应式适配更好
    contentHeightStyleWithComplexHeaderEnhanced() {
      const headerHeight = this.safeAreaInfo.topSafeDistanceRpx + 272
      return {
        height: `calc(100vh - ${headerHeight}rpx)`,
        marginTop: '0rpx'
      }
    },

    // 【新增】计算内容区域的准确高度 - 简单固定头部版本
    contentHeightStyleWithSimpleHeader() {
      const headerHeight = this.safeAreaInfo.topSafeDistanceRpx + 120
      return {
        height: `calc(100vh - ${headerHeight}rpx)`,
        marginTop: '0rpx'
      }
    },

    // 【新增】计算内容区域的准确高度 - 中等固定头部版本
    contentHeightStyleWithMediumHeader() {
      const headerHeight = this.safeAreaInfo.topSafeDistanceRpx + 88
      return {
        height: `calc(100vh - ${headerHeight}rpx)`,
        marginTop: '0rpx'
      }
    },

    // 【新增】计算内容区域的准确高度 - 复杂固定头部版本
    contentHeightStyleWithComplexHeader() {
      const headerHeight = this.safeAreaInfo.topSafeDistanceRpx + 212
      return {
        height: `calc(100vh - ${headerHeight}rpx)`,
        marginTop: '0rpx'
      }
    }
  },
  
  created() {
    // 初始化安全区域信息
    this.initSafeArea()
  },
  
  methods: {
    /**
     * 初始化安全区域信息
     */
    initSafeArea() {
      try {
        const safeAreaData = getSystemSafeArea()
        const topSafeDistanceRpx = getTopSafeDistanceRpx()
        
        this.safeAreaInfo = {
          statusBarHeight: safeAreaData.statusBarHeight,
          topSafeDistance: safeAreaData.topSafeDistance,
          topSafeDistanceRpx: topSafeDistanceRpx,
          platform: safeAreaData.platform,
          safeAreaInsets: safeAreaData.safeAreaInsets
        }
        
        console.log('安全区域信息初始化完成:', this.safeAreaInfo)
      } catch (error) {
        console.error('初始化安全区域信息失败:', error)
        // 设置默认值
        this.safeAreaInfo = {
          statusBarHeight: 20,
          topSafeDistance: 64,
          topSafeDistanceRpx: 128,
          platform: 'unknown',
          safeAreaInsets: { top: 20, bottom: 0, left: 0, right: 0 }
        }
      }
    },
    
    /**
     * 获取当前页面是否需要安全区域适配
     * 可以在具体页面中重写此方法来控制是否启用安全区域
     * @returns {boolean}
     */
    needSafeAreaAdapt() {
      // 默认返回true，表示需要安全区域适配
      // 具体页面可以重写此方法
      return true
    },
    
    /**
     * 获取安全区域信息
     * @returns {Object}
     */
    getSafeAreaInfo() {
      return this.safeAreaInfo
    },

    /**
     * 获取内容区域样式的使用说明
     * @returns {Object} 包含不同样式的使用说明
     */
    getContentStyleGuide() {
      return {
        // 传统的 marginTop 方案（兼容性保留）
        contentTopMarginStyle: '基础版本，只考虑安全区域，适用于没有固定头部的页面',
        contentTopMarginStyleWithSimpleHeader: '简单固定头部版本，适用于只有一行导航栏的页面（如 index.vue, detail.vue）- 120rpx',
        contentTopMarginStyleWithMediumHeader: '中等固定头部版本，适用于表单页面的导航栏（如 submit-clue.vue, submit-report.vue）- 88rpx',
        contentTopMarginStyleWithComplexHeader: '复杂固定头部版本，适用于有搜索框和标签导航的页面（如 clue.vue）- 212rpx',
        contentTopMarginStyleWithComplexHeaderEnhanced: '复杂固定头部增强版本，适用于需要更多间距的复杂页面（如 evaluation.vue）- 252rpx',

        // 新的 calc() 高度方案（推荐使用）
        contentHeightStyleWithSimpleHeader: '【推荐】简单固定头部高度版本，使用calc()精确计算，避免布局冲突',
        contentHeightStyleWithMediumHeader: '【推荐】中等固定头部高度版本，使用calc()精确计算，避免布局冲突',
        contentHeightStyleWithComplexHeader: '【推荐】复杂固定头部高度版本，使用calc()精确计算，避免布局冲突',
        contentHeightStyleWithComplexHeaderEnhanced: '【推荐】复杂固定头部增强高度版本，使用calc()精确计算，避免布局冲突，适用于evaluation.vue'
      }
    },

    /**
     * 获取内容区域顶部间距的使用说明（兼容性方法，建议使用getContentStyleGuide）
     * @returns {Object} 包含不同样式的使用说明
     * @deprecated 建议使用 getContentStyleGuide() 方法
     */
    getContentTopMarginStyleGuide() {
      const guide = this.getContentStyleGuide()
      return {
        contentTopMarginStyle: guide.contentTopMarginStyle,
        contentTopMarginStyleWithSimpleHeader: guide.contentTopMarginStyleWithSimpleHeader,
        contentTopMarginStyleWithMediumHeader: guide.contentTopMarginStyleWithMediumHeader,
        contentTopMarginStyleWithComplexHeader: guide.contentTopMarginStyleWithComplexHeader,
        contentTopMarginStyleWithComplexHeaderEnhanced: guide.contentTopMarginStyleWithComplexHeaderEnhanced
      }
    },

    /**
     * 强制刷新安全区域样式计算（用于下拉刷新后重新计算）
     */
    refreshSafeAreaStyles() {
      try {
        // 重新初始化安全区域信息
        this.initSafeArea()
        // 强制触发计算属性重新计算
        this.$forceUpdate()
        console.log('安全区域样式已刷新')
      } catch (error) {
        console.error('刷新安全区域样式失败:', error)
      }
    },

    /**
     * 启动安全区域样式保护机制（防止滚动等操作导致样式失效）
     */
    startSafeAreaProtection() {
      // 清除之前的定时器
      if (this.safeAreaProtectionTimer) {
        clearInterval(this.safeAreaProtectionTimer)
      }

      // 每隔2秒检查一次安全区域样式是否正确
      this.safeAreaProtectionTimer = setInterval(() => {
        if (this.needSafeAreaAdapt()) {
          this.refreshSafeAreaStyles()
        }
      }, 2000)
    },

    /**
     * 停止安全区域样式保护机制
     */
    stopSafeAreaProtection() {
      if (this.safeAreaProtectionTimer) {
        clearInterval(this.safeAreaProtectionTimer)
        this.safeAreaProtectionTimer = null
      }
    }
  }
}
