<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			
			// 初始化用户状态
			this.$store.dispatch('initUserState')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "uview-ui/index.scss";

	/* 全局样式 */
	page {
		background-color: #f5f5f5;
	}

	/* 页面内容区域样式，为tabbar预留空间 */
	.page-content {
		padding-bottom: 60px; /* tabbar高度 */
	}

	/* 安全区域适配 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	/* 修复 u-loadmore 组件"没有更多了"文字行高问题 */
	.u-loadmore .u-loadmore__content__text,
	.u-loadmore .u-loadmore__content__dot-text {
		line-height: 1.5 !important; /* 设置更合适的行高，确保文字完整显示 */
		min-height: 40rpx !important; /* 设置最小高度，防止文字被截断 */
	}
</style>
